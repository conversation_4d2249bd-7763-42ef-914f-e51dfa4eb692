<template>
  <div class="home-container">
    <!-- 用户欢迎区域 -->
    <div class="welcome-section">
      <van-card
        :thumb="userStore.user?.avatar || defaultAvatar"
        :title="`欢迎回来，${userStore.user?.name || userStore.user?.username}！`"
        :desc="getCurrentTimeGreeting()"
        thumb-link="#"
      >
        <template #tags>
          <van-tag type="success" size="medium">在线</van-tag>
        </template>
      </van-card>
    </div>

    <!-- 快捷操作区域 -->
    <div class="quick-actions">
      <van-grid :column-num="2" :gutter="16">
        <van-grid-item
          icon="photograph"
          text="人脸签到"
          @click="goToCheckIn"
          class="action-item checkin-action"
        />
        <van-grid-item
          icon="clock-o"
          text="签到记录"
          @click="showToast('功能开发中...')"
          class="action-item"
        />
        <van-grid-item
          icon="user-o"
          text="个人信息"
          @click="goToProfile"
          class="action-item"
        />
        <van-grid-item
          icon="setting-o"
          text="系统设置"
          @click="showToast('功能开发中...')"
          class="action-item"
        />
      </van-grid>
    </div>

    <!-- 今日统计 -->
    <div class="stats-section">
      <van-cell-group inset>
        <van-cell title="今日签到状态" :value="todayCheckInStatus" />
        <van-cell title="本月签到次数" :value="`${monthlyCheckIns} 次`" />
        <van-cell title="签到成功率" :value="`${successRate}%`" />
      </van-cell-group>
    </div>

    <!-- 最近签到记录 -->
    <div class="recent-records">
      <van-cell-group inset>
        <van-cell title="最近签到记录" />
        <van-empty
          v-if="recentRecords.length === 0"
          description="暂无签到记录"
          image="search"
        />
        <van-cell
          v-for="record in recentRecords"
          :key="record.id"
          :title="formatDate(record.checkInTime)"
          :value="record.status === 'success' ? '成功' : '失败'"
          :label="`相似度: ${record.similarity}%`"
        >
          <template #icon>
            <van-icon
              :name="record.status === 'success' ? 'success' : 'fail'"
              :color="record.status === 'success' ? '#07c160' : '#ee0a24'"
            />
          </template>
        </van-cell>
      </van-cell-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/counter'
import type { CheckInRecord } from '@/types'

const router = useRouter()
const userStore = useUserStore()

// 默认头像
const defaultAvatar = 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'

// 模拟数据
const recentRecords = ref<CheckInRecord[]>([])
const monthlyCheckIns = ref(15)
const successRate = ref(95)

// 今日签到状态
const todayCheckInStatus = computed(() => {
  const today = new Date().toDateString()
  const todayRecord = recentRecords.value.find(record =>
    new Date(record.checkInTime).toDateString() === today
  )
  return todayRecord ? '已签到' : '未签到'
})

// 获取当前时间问候语
const getCurrentTimeGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了，注意休息'
  if (hour < 12) return '早上好，新的一天开始了'
  if (hour < 18) return '下午好，工作顺利'
  return '晚上好，辛苦了'
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 跳转到签到页面
const goToCheckIn = () => {
  router.push('/checkin')
}

// 跳转到个人中心
const goToProfile = () => {
  router.push('/profile')
}

// 加载数据
const loadData = async () => {
  // 这里可以调用API获取真实数据
  // 暂时使用模拟数据
  recentRecords.value = [
    {
      id: 1,
      userId: 1,
      photoUrl: '',
      checkInTime: new Date().toISOString(),
      status: 'success',
      similarity: 98
    },
    {
      id: 2,
      userId: 1,
      photoUrl: '',
      checkInTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      status: 'success',
      similarity: 95
    }
  ]
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.home-container {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100%;
  width: 100%;
}

.welcome-section {
  margin-bottom: 20px;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-item {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.checkin-action {
  background: linear-gradient(135deg, #1989fa, #1976d2);
  color: white;
}

.stats-section {
  margin-bottom: 20px;
}

.recent-records {
  margin-bottom: 20px;
}

:deep(.van-card__thumb) {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

:deep(.van-grid-item__content) {
  padding: 20px 16px;
}

:deep(.checkin-action .van-grid-item__icon) {
  color: white;
}

:deep(.checkin-action .van-grid-item__text) {
  color: white;
}
</style>
