// API响应统一格式
export interface ResultVO<T = any> {
  code: number
  message: string
  data?: T
}

// 用户相关类型
export interface User {
  id?: number
  username: string
  email?: string
  phone?: string
  name?: string
  avatar?: string
  isActive?: boolean
  createdAt?: string
  updatedAt?: string
}

// 用户注册请求
export interface RegisterRequest {
  username: string
  password: string
  email?: string
  phone?: string
  name?: string
  avatar?: string // 人脸照片URL
}

// 用户登录请求
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应
export interface LoginResponse {
  token: string
  user: User
}

// 照片相关类型
export interface PhotoVO {
  url: string
}

// 签到记录
export interface CheckInRecord {
  id?: number
  userId: number
  photoUrl: string
  checkInTime: string
  location?: string
  status: 'success' | 'failed'
  similarity?: number
}

// 路由元信息
export interface RouteMeta {
  title?: string
  requiresAuth?: boolean
  hideNavBar?: boolean
}
