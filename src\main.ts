import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// Vant UI组件
import Vant from 'vant'
import 'vant/lib/index.css'

import App from './App.vue'
import router from './router'
import { useUserStore } from './stores/counter'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(Vant)

// 初始化用户认证状态
const userStore = useUserStore()
userStore.initializeAuth()

app.mount('#app')
