<template>
  <div class="checkin-container">
    <!-- 签到状态显示 -->
    <div class="status-section">
      <div class="status-card" :class="{ 'checked-in': hasCheckedInToday }">
        <van-icon 
          :name="hasCheckedInToday ? 'success' : 'clock-o'" 
          :color="hasCheckedInToday ? '#07c160' : '#1989fa'"
          size="60"
        />
        <h2>{{ hasCheckedInToday ? '今日已签到' : '等待签到' }}</h2>
        <p>{{ currentTime }}</p>
        <p v-if="hasCheckedInToday" class="checkin-time">
          签到时间: {{ todayCheckInTime }}
        </p>
      </div>
    </div>

    <!-- 拍照签到区域 -->
    <div class="camera-section" v-if="!hasCheckedInToday">
      <van-cell-group inset>
        <van-cell title="人脸识别签到" />
        <div class="camera-area">
          <!-- 相机预览 -->
          <div class="camera-preview" v-if="showCamera">
            <video 
              ref="videoRef" 
              autoplay 
              playsinline 
              muted
              class="camera-video"
            ></video>
            <canvas 
              ref="canvasRef" 
              class="camera-canvas"
              style="display: none;"
            ></canvas>
          </div>
          
          <!-- 照片预览 -->
          <div class="photo-preview" v-else-if="capturedPhoto">
            <img :src="capturedPhoto" alt="拍摄的照片" />
          </div>
          
          <!-- 默认状态 -->
          <div class="camera-placeholder" v-else>
            <van-icon name="photograph" size="80" color="#dcdee0" />
            <p>点击下方按钮开始拍照</p>
          </div>
        </div>
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button
          v-if="!showCamera && !capturedPhoto"
          type="primary"
          size="large"
          round
          block
          @click="startCamera"
          :loading="cameraLoading"
        >
          <van-icon name="photograph" />
          开启相机
        </van-button>

        <div v-else-if="showCamera" class="camera-controls">
          <van-button
            type="default"
            size="large"
            round
            @click="stopCamera"
          >
            取消
          </van-button>
          <van-button
            type="primary"
            size="large"
            round
            @click="capturePhoto"
            class="capture-btn"
          >
            <van-icon name="photograph" />
            拍照
          </van-button>
        </div>

        <div v-else-if="capturedPhoto" class="photo-controls">
          <van-button
            type="default"
            size="large"
            round
            @click="retakePhoto"
          >
            重拍
          </van-button>
          <van-button
            type="primary"
            size="large"
            round
            @click="submitCheckIn"
            :loading="submitting"
            loading-text="识别中..."
          >
            确认签到
          </van-button>
        </div>
      </div>
    </div>

    <!-- 今日已签到的提示 -->
    <div class="checked-in-section" v-else>
      <van-cell-group inset>
        <van-cell title="签到成功" />
        <div class="success-message">
          <van-icon name="success" size="60" color="#07c160" />
          <p>您今日已完成签到</p>
          <p class="similarity">识别相似度: {{ todayCheckInSimilarity }}%</p>
        </div>
      </van-cell-group>
    </div>

    <!-- 签到记录 -->
    <div class="history-section">
      <van-cell-group inset>
        <van-cell title="最近签到记录" />
        <van-cell
          v-for="record in recentRecords"
          :key="record.id"
          :title="formatDate(record.checkInTime)"
          :value="record.status === 'success' ? '成功' : '失败'"
          :label="`相似度: ${record.similarity}%`"
        >
          <template #icon>
            <van-icon
              :name="record.status === 'success' ? 'success' : 'fail'"
              :color="record.status === 'success' ? '#07c160' : '#ee0a24'"
            />
          </template>
        </van-cell>
      </van-cell-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { showToast, showDialog } from 'vant'
import { usePhotoStore } from '@/stores/photo'
import type { CheckInRecord } from '@/types'

const photoStore = usePhotoStore()

// 相机相关
const videoRef = ref<HTMLVideoElement>()
const canvasRef = ref<HTMLCanvasElement>()
const showCamera = ref(false)
const cameraLoading = ref(false)
const mediaStream = ref<MediaStream | null>(null)

// 照片相关
const capturedPhoto = ref('')
const submitting = ref(false)

// 时间相关
const currentTime = ref('')
const todayCheckInTime = ref('')
const todayCheckInSimilarity = ref(98)

// 签到记录
const recentRecords = ref<CheckInRecord[]>([])

// 是否已签到
const hasCheckedInToday = computed(() => {
  const today = new Date().toDateString()
  return recentRecords.value.some(record => 
    new Date(record.checkInTime).toDateString() === today
  )
})

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 开启相机
const startCamera = async () => {
  cameraLoading.value = true
  
  try {
    const stream = await navigator.mediaDevices.getUserMedia({
      video: { 
        facingMode: 'user',
        width: { ideal: 640 },
        height: { ideal: 480 }
      }
    })
    
    mediaStream.value = stream
    showCamera.value = true
    
    if (videoRef.value) {
      videoRef.value.srcObject = stream
    }
  } catch (error) {
    showToast.fail('无法访问相机，请检查权限设置')
    console.error('Camera access error:', error)
  } finally {
    cameraLoading.value = false
  }
}

// 停止相机
const stopCamera = () => {
  if (mediaStream.value) {
    mediaStream.value.getTracks().forEach(track => track.stop())
    mediaStream.value = null
  }
  showCamera.value = false
}

// 拍照
const capturePhoto = () => {
  if (!videoRef.value || !canvasRef.value) return
  
  const video = videoRef.value
  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')!
  
  // 设置画布尺寸
  canvas.width = video.videoWidth
  canvas.height = video.videoHeight
  
  // 绘制当前帧
  ctx.drawImage(video, 0, 0)
  
  // 转换为base64
  capturedPhoto.value = canvas.toDataURL('image/jpeg', 0.8)
  
  // 停止相机
  stopCamera()
}

// 重新拍照
const retakePhoto = () => {
  capturedPhoto.value = ''
  startCamera()
}

// 提交签到
const submitCheckIn = async () => {
  if (!capturedPhoto.value) return
  
  submitting.value = true
  
  try {
    // 将base64转换为File对象
    const response = await fetch(capturedPhoto.value)
    const blob = await response.blob()
    const file = new File([blob], 'checkin.jpg', { type: 'image/jpeg' })
    
    // 上传照片
    const uploadResult = await photoStore.uploadPhoto(file)
    
    if (uploadResult.success) {
      // 模拟人脸识别结果
      const similarity = Math.floor(Math.random() * 20) + 80 // 80-99%
      const success = similarity >= 85
      
      if (success) {
        // 添加签到记录
        const newRecord: CheckInRecord = {
          id: Date.now(),
          userId: 1,
          photoUrl: uploadResult.data!.url,
          checkInTime: new Date().toISOString(),
          status: 'success',
          similarity
        }
        
        recentRecords.value.unshift(newRecord)
        todayCheckInTime.value = formatDate(newRecord.checkInTime)
        todayCheckInSimilarity.value = similarity
        
        showToast.success(`签到成功！相似度: ${similarity}%`)
      } else {
        showDialog({
          title: '签到失败',
          message: `人脸识别相似度过低 (${similarity}%)，请重新拍照`,
          confirmButtonText: '重新拍照'
        }).then(() => {
          retakePhoto()
        })
      }
    } else {
      showToast.fail(uploadResult.message)
    }
  } catch (error) {
    showToast.fail('签到失败，请重试')
  } finally {
    submitting.value = false
    capturedPhoto.value = ''
  }
}

// 加载签到记录
const loadCheckInRecords = () => {
  // 模拟数据
  recentRecords.value = [
    {
      id: 1,
      userId: 1,
      photoUrl: '',
      checkInTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      status: 'success',
      similarity: 95
    },
    {
      id: 2,
      userId: 1,
      photoUrl: '',
      checkInTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'success',
      similarity: 92
    }
  ]
}

onMounted(() => {
  updateCurrentTime()
  loadCheckInRecords()
  
  // 每秒更新时间
  const timeInterval = setInterval(updateCurrentTime, 1000)
  
  onUnmounted(() => {
    clearInterval(timeInterval)
    stopCamera()
  })
})
</script>

<style scoped>
.checkin-container {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.status-section {
  margin-bottom: 20px;
}

.status-card {
  background: white;
  border-radius: 12px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.status-card.checked-in {
  background: linear-gradient(135deg, #07c160, #06ad56);
  color: white;
}

.status-card h2 {
  margin: 16px 0 8px 0;
  font-size: 24px;
  font-weight: bold;
}

.status-card p {
  margin: 4px 0;
  opacity: 0.8;
}

.checkin-time {
  font-size: 14px;
  margin-top: 8px !important;
}

.camera-section {
  margin-bottom: 20px;
}

.camera-area {
  padding: 16px;
  text-align: center;
}

.camera-preview {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
}

.camera-video {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.photo-preview img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
}

.camera-placeholder {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f7f8fa;
  border: 2px dashed #dcdee0;
  border-radius: 8px;
}

.camera-placeholder p {
  margin: 16px 0 0 0;
  color: #969799;
}

.action-buttons {
  padding: 20px 0;
}

.camera-controls {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.photo-controls {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.capture-btn {
  flex: 1;
  max-width: 200px;
}

.checked-in-section {
  margin-bottom: 20px;
}

.success-message {
  padding: 30px 20px;
  text-align: center;
}

.success-message p {
  margin: 16px 0 8px 0;
  font-size: 16px;
}

.similarity {
  color: #07c160;
  font-weight: bold;
}

.history-section {
  margin-bottom: 20px;
}
</style>
