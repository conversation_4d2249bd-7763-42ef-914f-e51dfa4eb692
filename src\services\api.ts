import axios, { type AxiosInstance, type AxiosResponse } from 'axios'
import type {
  ResultVO,
  User,
  RegisterRequest,
  LoginRequest,
  LoginResponse,
  PhotoVO
} from '@/types'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: 'http://127.0.0.1:8080',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = token
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 统一处理响应
api.interceptors.response.use(
  (response: AxiosResponse<ResultVO>) => {
    const { data, status } = response

    // 处理204 No Content (删除用户成功)
    if (status === 204) {
      return response
    }

    // 如果是成功响应，直接返回数据
    if (data && data.code >= 200 && data.code < 300) {
      return response
    }

    // 如果是401未授权，清除token并跳转到登录页
    if (data && data.code === 401) {
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }

    // 其他错误情况
    return Promise.reject(new Error(data?.message || '请求失败'))
  },
  (error) => {
    // 网络错误或其他错误
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }

    return Promise.reject(error)
  }
)

// 用户相关API
export const userApi = {
  // 用户注册
  register: (data: RegisterRequest): Promise<AxiosResponse<ResultVO<User>>> => {
    return api.post('/users', data)
  },

  // 用户登录
  login: (data: LoginRequest): Promise<AxiosResponse<ResultVO<LoginResponse>>> => {
    return api.post('/users/login', data)
  },

  // 获取用户信息
  getUserInfo: (): Promise<AxiosResponse<ResultVO<User>>> => {
    return api.get('/users')
  },

  // 删除用户（注销）
  deleteUser: (): Promise<AxiosResponse<ResultVO>> => {
    return api.delete('/users')
  }
}

// 照片相关API
export const photoApi = {
  // 上传照片 - 不需要token
  uploadPhoto: (file: File): Promise<AxiosResponse<ResultVO<PhotoVO>>> => {
    const formData = new FormData()
    formData.append('file', file)

    // 创建不带token的axios实例用于照片上传
    const photoUploadApi = axios.create({
      baseURL: 'http://127.0.0.1:8080',
      timeout: 10000
    })

    return photoUploadApi.post('/photos', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 签到相关API (根据实际后端接口补充)
export const checkInApi = {
  // 提交签到 - 需要token
  submitCheckIn: (data: { photoUrl: string }): Promise<AxiosResponse<ResultVO<any>>> => {
    return api.post('/checkin', data)
  },

  // 获取签到记录 - 需要token
  getCheckInRecords: (params?: {
    page?: number,
    size?: number,
    status?: string,
    startDate?: string,
    endDate?: string
  }): Promise<AxiosResponse<ResultVO<any>>> => {
    return api.get('/checkin/records', { params })
  },

  // 获取用户签到统计 - 需要token
  getCheckInStats: (): Promise<AxiosResponse<ResultVO<any>>> => {
    return api.get('/checkin/stats')
  }
}

export default api
