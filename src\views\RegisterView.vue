<template>
  <div class="register-container">
    <div class="register-header">
      <div class="logo">
        <van-icon name="add-square" size="80" color="#1989fa" />
      </div>
      <h1 class="title">创建新账户</h1>
      <p class="subtitle">请填写注册信息</p>
    </div>

    <van-form @submit="handleRegister" class="register-form">
      <van-cell-group inset>
        <van-field
          v-model="registerForm.username"
          name="username"
          label="用户名"
          placeholder="请输入用户名"
          :rules="[
            { required: true, message: '请输入用户名' },
            { min: 3, message: '用户名至少3个字符' }
          ]"
          left-icon="user-o"
          clearable
        />
        <van-field
          v-model="registerForm.password"
          type="password"
          name="password"
          label="密码"
          placeholder="请输入密码"
          :rules="[
            { required: true, message: '请输入密码' },
            { min: 6, message: '密码至少6个字符' }
          ]"
          left-icon="lock"
          clearable
        />
        <van-field
          v-model="confirmPassword"
          type="password"
          name="confirmPassword"
          label="确认密码"
          placeholder="请再次输入密码"
          :rules="[
            { required: true, message: '请确认密码' },
            { validator: validatePassword, message: '两次密码输入不一致' }
          ]"
          left-icon="lock"
          clearable
        />
        <van-field
          v-model="registerForm.name"
          name="name"
          label="姓名"
          placeholder="请输入真实姓名"
          :rules="[{ required: true, message: '请输入姓名' }]"
          left-icon="contact"
          clearable
        />
        <van-field
          v-model="registerForm.email"
          name="email"
          label="邮箱"
          placeholder="请输入邮箱地址（可选）"
          left-icon="envelop-o"
          clearable
        />
        <van-field
          v-model="registerForm.phone"
          name="phone"
          label="手机号"
          placeholder="请输入手机号（可选）"
          left-icon="phone-o"
          clearable
        />
      </van-cell-group>

      <!-- 人脸照片上传 -->
      <van-cell-group inset class="photo-upload-group">
        <van-cell title="人脸照片" :value="photoStatus" />
        <div class="photo-upload-area">
          <van-uploader
            v-model="fileList"
            :after-read="handlePhotoUpload"
            :before-delete="handlePhotoDelete"
            accept="image/*"
            :max-count="1"
            :preview-size="100"
            upload-text="上传人脸照片"
          />
          <p class="upload-tip">请上传清晰的正面人脸照片，用于后续签到识别</p>
        </div>
      </van-cell-group>

      <div class="register-actions">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="loading"
          loading-text="注册中..."
          size="large"
          :disabled="!canSubmit"
        >
          注册
        </van-button>
      </div>
    </van-form>

    <div class="register-footer">
      <van-divider>已有账户？</van-divider>
      <van-button
        type="default"
        size="large"
        round
        block
        @click="goToLogin"
      >
        立即登录
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/counter'
import { usePhotoStore } from '@/stores/photo'
import type { RegisterRequest } from '@/types'
import type { UploaderFileListItem } from 'vant'

const router = useRouter()
const userStore = useUserStore()
const photoStore = usePhotoStore()

// 表单数据
const registerForm = reactive<RegisterRequest>({
  username: '',
  password: '',
  name: '',
  email: '',
  phone: '',
  avatar: ''
})

// 确认密码
const confirmPassword = ref('')

// 文件列表
const fileList = ref<UploaderFileListItem[]>([])

// 加载状态
const loading = ref(false)

// 照片状态
const photoStatus = computed(() => {
  if (registerForm.avatar) {
    return '已上传'
  }
  return '未上传'
})

// 是否可以提交
const canSubmit = computed(() => {
  return registerForm.username &&
         registerForm.password &&
         registerForm.name &&
         registerForm.avatar &&
         confirmPassword.value === registerForm.password
})

// 密码验证
const validatePassword = (value: string) => {
  return value === registerForm.password
}

// 处理照片上传
const handlePhotoUpload = async (file: UploaderFileListItem) => {
  if (!file.file) return

  // 验证文件
  const validation = photoStore.validateImageFile(file.file)
  if (!validation.valid) {
    showToast.fail(validation.message)
    return
  }

  try {
    // 压缩图片
    const compressedFile = await photoStore.compressImage(file.file)

    // 上传照片
    const result = await photoStore.uploadPhoto(compressedFile)

    if (result.success) {
      registerForm.avatar = result.data!.url
      showToast.success('照片上传成功')
    } else {
      showToast.fail(result.message)
      // 移除失败的文件
      fileList.value = []
    }
  } catch (error) {
    showToast.fail('照片上传失败')
    fileList.value = []
  }
}

// 处理照片删除
const handlePhotoDelete = () => {
  registerForm.avatar = ''
  return true
}

// 处理注册
const handleRegister = async () => {
  if (!canSubmit.value) {
    showToast.fail('请完善注册信息')
    return
  }

  loading.value = true

  try {
    const result = await userStore.register(registerForm)

    if (result.success) {
      showToast.success(result.message)
      // 注册成功后跳转到登录页面
      router.replace('/login')
    } else {
      showToast.fail(result.message)
    }
  } catch (error) {
    showToast.fail('注册失败，请重试')
  } finally {
    loading.value = false
  }
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.register-container {
  min-height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  padding-top: 40px;
  overflow-y: auto;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  margin-bottom: 20px;
}

.title {
  color: white;
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 10px 0;
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

.register-form {
  margin-bottom: 20px;
}

.photo-upload-group {
  margin-top: 16px;
}

.photo-upload-area {
  padding: 16px;
  text-align: center;
}

.upload-tip {
  font-size: 12px;
  color: #969799;
  margin: 8px 0 0 0;
  line-height: 1.4;
}

.register-actions {
  padding: 20px 16px 0;
}

.register-footer {
  padding: 0 16px;
}

:deep(.van-cell-group) {
  margin-bottom: 0;
}

:deep(.van-field__left-icon) {
  color: #1989fa;
}

:deep(.van-divider) {
  color: rgba(255, 255, 255, 0.8);
  border-color: rgba(255, 255, 255, 0.3);
}

:deep(.van-uploader__upload) {
  background-color: #f7f8fa;
  border: 2px dashed #dcdee0;
}

:deep(.van-uploader__upload-icon) {
  color: #1989fa;
}
</style>
