<template>
  <div class="admin-photos-container">
    <!-- 上传区域 -->
    <div class="upload-section">
      <van-cell-group inset>
        <van-cell title="上传人脸照片" />
        <div class="upload-area">
          <van-uploader
            v-model="fileList"
            :after-read="handlePhotoUpload"
            :before-delete="handlePhotoDelete"
            accept="image/*"
            multiple
            :max-count="10"
            :preview-size="80"
            upload-text="选择照片"
          />
          <p class="upload-tip">
            支持批量上传，每次最多10张照片<br>
            支持JPG、PNG格式，单张照片不超过5MB
          </p>
        </div>
      </van-cell-group>
    </div>

    <!-- 照片列表 -->
    <div class="photos-section">
      <van-cell-group inset>
        <van-cell title="已上传照片" :value="`共 ${photos.length} 张`" />
        <van-empty
          v-if="photos.length === 0"
          description="暂无照片"
          image="photo"
        />
        <div v-else class="photos-grid">
          <div
            v-for="photo in photos"
            :key="photo.id"
            class="photo-item"
            @click="previewPhoto(photo)"
          >
            <van-image
              :src="photo.url"
              fit="cover"
              width="100"
              height="100"
              radius="8"
            />
            <div class="photo-info">
              <p class="photo-name">{{ photo.name }}</p>
              <p class="photo-time">{{ formatDate(photo.uploadTime) }}</p>
            </div>
            <van-button
              type="danger"
              size="mini"
              round
              class="delete-btn"
              @click.stop="deletePhoto(photo)"
            >
              删除
            </van-button>
          </div>
        </div>
      </van-cell-group>
    </div>

    <!-- 照片预览 -->
    <van-image-preview
      v-model:show="showPreview"
      :images="previewImages"
      :start-position="previewIndex"
      @change="onPreviewChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { usePhotoStore } from '@/stores/photo'
import type { UploaderFileListItem } from 'vant'

const photoStore = usePhotoStore()

// 文件列表
const fileList = ref<UploaderFileListItem[]>([])

// 照片列表
const photos = ref([
  {
    id: 1,
    name: '用户头像1.jpg',
    url: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
    uploadTime: new Date().toISOString()
  },
  {
    id: 2,
    name: '用户头像2.jpg',
    url: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
    uploadTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
  }
])

// 预览相关
const showPreview = ref(false)
const previewIndex = ref(0)
const previewImages = computed(() => photos.value.map(photo => photo.url))

// 处理照片上传
const handlePhotoUpload = async (file: UploaderFileListItem) => {
  if (!file.file) return

  // 验证文件
  const validation = photoStore.validateImageFile(file.file)
  if (!validation.valid) {
    showToast.fail(validation.message)
    return
  }

  try {
    // 压缩图片
    const compressedFile = await photoStore.compressImage(file.file)

    // 上传照片
    const result = await photoStore.uploadPhoto(compressedFile)

    if (result.success) {
      // 添加到照片列表
      photos.value.unshift({
        id: Date.now(),
        name: file.file.name,
        url: result.data!.url,
        uploadTime: new Date().toISOString()
      })
      showToast.success('照片上传成功')
    } else {
      showToast.fail(result.message)
    }
  } catch (error) {
    showToast.fail('照片上传失败')
  }
}

// 处理照片删除（上传组件）
const handlePhotoDelete = () => {
  return true
}

// 预览照片
const previewPhoto = (photo: any) => {
  previewIndex.value = photos.value.findIndex(p => p.id === photo.id)
  showPreview.value = true
}

// 预览切换
const onPreviewChange = (index: number) => {
  previewIndex.value = index
}

// 删除照片
const deletePhoto = async (photo: any) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除照片 "${photo.name}" 吗？`
    })

    // 从列表中移除
    const index = photos.value.findIndex(p => p.id === photo.id)
    if (index > -1) {
      photos.value.splice(index, 1)
      showToast.success('删除成功')
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}
</script>

<style scoped>
.admin-photos-container {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100%;
  width: 100%;
}

.upload-section {
  margin-bottom: 20px;
}

.upload-area {
  padding: 16px;
  text-align: center;
}

.upload-tip {
  font-size: 12px;
  color: #969799;
  margin: 12px 0 0 0;
  line-height: 1.4;
}

.photos-section {
  margin-bottom: 20px;
}

.photos-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.photo-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.photo-info {
  flex: 1;
}

.photo-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #323233;
}

.photo-time {
  margin: 0;
  font-size: 12px;
  color: #969799;
}

.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
}

:deep(.van-uploader__upload) {
  background-color: #f7f8fa;
  border: 2px dashed #dcdee0;
}

:deep(.van-uploader__upload-icon) {
  color: #1989fa;
}
</style>
