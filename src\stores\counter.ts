import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { User, LoginRequest, RegisterRequest } from '@/types'
import { userApi } from '@/services/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoggedIn = computed(() => !!token.value && !!user.value)

  // 初始化 - 从localStorage恢复状态
  const initializeAuth = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')

    if (savedToken && savedUser) {
      token.value = savedToken
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('Failed to parse saved user data:', error)
        clearAuth()
      }
    }
  }

  // 清除认证信息
  const clearAuth = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 保存认证信息
  const saveAuth = (authToken: string, userData: User) => {
    token.value = authToken
    user.value = userData
    localStorage.setItem('token', authToken)
    localStorage.setItem('user', JSON.stringify(userData))
  }

  // 用户登录
  const login = async (loginData: LoginRequest) => {
    try {
      const response = await userApi.login(loginData)
      const { token: authToken, user: userData } = response.data.data!

      saveAuth(authToken, userData)
      return { success: true, message: '登录成功' }
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '登录失败'
      }
    }
  }

  // 用户注册
  const register = async (registerData: RegisterRequest) => {
    try {
      const response = await userApi.register(registerData)
      return {
        success: true,
        message: '注册成功，请登录',
        data: response.data.data
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '注册失败'
      }
    }
  }

  // 用户注销
  const logout = async () => {
    try {
      await userApi.deleteUser()
      clearAuth()
      return { success: true, message: '注销成功' }
    } catch (error: any) {
      // 即使API调用失败，也要清除本地状态
      clearAuth()
      return {
        success: false,
        message: error.message || '注销失败'
      }
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await userApi.getUserInfo()
      user.value = response.data.data!
      localStorage.setItem('user', JSON.stringify(user.value))
      return { success: true, data: user.value }
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '获取用户信息失败'
      }
    }
  }

  return {
    // 状态
    user,
    token,
    isLoggedIn,

    // 方法
    initializeAuth,
    clearAuth,
    login,
    register,
    logout,
    fetchUserInfo
  }
})
