<template>
  <div class="admin-records-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <van-cell-group inset>
        <van-cell title="筛选条件" />
        <van-field
          v-model="filterForm.keyword"
          label="关键词"
          placeholder="搜索用户名或姓名"
          clearable
        />
        <van-field
          v-model="filterForm.status"
          label="签到状态"
          placeholder="选择状态"
          readonly
          @click="showStatusPicker = true"
        />
        <van-field
          v-model="filterForm.dateRange"
          label="日期范围"
          placeholder="选择日期范围"
          readonly
          @click="showDatePicker = true"
        />
        <div class="filter-actions">
          <van-button type="primary" size="small" @click="applyFilter">
            筛选
          </van-button>
          <van-button type="default" size="small" @click="resetFilter">
            重置
          </van-button>
        </div>
      </van-cell-group>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <van-cell-group inset>
        <van-cell title="统计信息" />
        <van-grid :column-num="3" :border="false">
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ stats.total }}</div>
              <div class="stat-label">总签到</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number success">{{ stats.success }}</div>
              <div class="stat-label">成功</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number failed">{{ stats.failed }}</div>
              <div class="stat-label">失败</div>
            </div>
          </van-grid-item>
        </van-grid>
      </van-cell-group>
    </div>

    <!-- 签到记录列表 -->
    <div class="records-section">
      <van-cell-group inset>
        <van-cell title="签到记录" :value="`共 ${filteredRecords.length} 条`" />
        <van-empty
          v-if="filteredRecords.length === 0"
          description="暂无记录"
          image="search"
        />
        <van-cell
          v-for="record in filteredRecords"
          :key="record.id"
          :title="record.userName"
          :value="record.status === 'success' ? '成功' : '失败'"
          :label="`${formatDate(record.checkInTime)} | 相似度: ${record.similarity}%`"
          @click="viewRecordDetail(record)"
          is-link
        >
          <template #icon>
            <van-avatar
              :src="record.userAvatar"
              size="40"
              style="margin-right: 12px"
            />
          </template>
          <template #value>
            <van-tag
              :type="record.status === 'success' ? 'success' : 'danger'"
              size="medium"
            >
              {{ record.status === 'success' ? '成功' : '失败' }}
            </van-tag>
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 状态选择器 -->
    <van-popup v-model:show="showStatusPicker" position="bottom">
      <van-picker
        :columns="statusColumns"
        @confirm="onStatusConfirm"
        @cancel="showStatusPicker = false"
      />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-calendar
        v-model:show="showDatePicker"
        type="range"
        @confirm="onDateConfirm"
      />
    </van-popup>

    <!-- 记录详情弹窗 -->
    <van-popup
      v-model:show="showRecordDetail"
      position="bottom"
      :style="{ height: '70%' }"
      round
    >
      <div class="record-detail-popup" v-if="selectedRecord">
        <van-nav-bar
          title="签到详情"
          left-text="关闭"
          @click-left="showRecordDetail = false"
        />
        <div class="record-detail-content">
          <van-cell-group inset>
            <van-cell title="用户信息" />
            <van-cell title="姓名" :value="selectedRecord.userName" />
            <van-cell title="用户名" :value="selectedRecord.username" />
            <van-cell title="签到时间" :value="formatDateTime(selectedRecord.checkInTime)" />
            <van-cell title="签到状态">
              <template #value>
                <van-tag
                  :type="selectedRecord.status === 'success' ? 'success' : 'danger'"
                  size="medium"
                >
                  {{ selectedRecord.status === 'success' ? '成功' : '失败' }}
                </van-tag>
              </template>
            </van-cell>
            <van-cell title="相似度" :value="`${selectedRecord.similarity}%`" />
            <van-cell title="签到照片" />
            <div class="photo-preview">
              <van-image
                :src="selectedRecord.photoUrl"
                width="200"
                height="200"
                fit="cover"
                radius="8"
                @click="previewPhoto(selectedRecord.photoUrl)"
              />
            </div>
          </van-cell-group>
        </div>
      </div>
    </van-popup>

    <!-- 照片预览 -->
    <van-image-preview
      v-model:show="showPhotoPreview"
      :images="[previewPhotoUrl]"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { showToast } from 'vant'
import type { CheckInRecord } from '@/types'

// 筛选表单
const filterForm = reactive({
  keyword: '',
  status: '',
  dateRange: ''
})

// 弹窗状态
const showStatusPicker = ref(false)
const showDatePicker = ref(false)
const showRecordDetail = ref(false)
const showPhotoPreview = ref(false)

// 选中的记录
const selectedRecord = ref<any>(null)
const previewPhotoUrl = ref('')

// 状态选项
const statusColumns = [
  { text: '全部', value: '' },
  { text: '成功', value: 'success' },
  { text: '失败', value: 'failed' }
]

// 模拟签到记录数据
const records = ref([
  {
    id: 1,
    userId: 1,
    userName: '张三',
    username: 'zhangsan',
    userAvatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
    photoUrl: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
    checkInTime: new Date().toISOString(),
    status: 'success',
    similarity: 98
  },
  {
    id: 2,
    userId: 2,
    userName: '李四',
    username: 'lisi',
    userAvatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
    photoUrl: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
    checkInTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    status: 'success',
    similarity: 95
  },
  {
    id: 3,
    userId: 3,
    userName: '王五',
    username: 'wangwu',
    userAvatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
    photoUrl: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
    checkInTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    status: 'failed',
    similarity: 72
  }
])

// 统计信息
const stats = computed(() => {
  const total = records.value.length
  const success = records.value.filter(r => r.status === 'success').length
  const failed = total - success
  return { total, success, failed }
})

// 筛选后的记录
const filteredRecords = computed(() => {
  let result = records.value

  // 关键词筛选
  if (filterForm.keyword) {
    const keyword = filterForm.keyword.toLowerCase()
    result = result.filter(record =>
      record.userName.toLowerCase().includes(keyword) ||
      record.username.toLowerCase().includes(keyword)
    )
  }

  // 状态筛选
  if (filterForm.status) {
    result = result.filter(record => record.status === filterForm.status)
  }

  return result
})

// 应用筛选
const applyFilter = () => {
  showToast('筛选已应用')
}

// 重置筛选
const resetFilter = () => {
  filterForm.keyword = ''
  filterForm.status = ''
  filterForm.dateRange = ''
  showToast('筛选已重置')
}

// 状态选择确认
const onStatusConfirm = ({ selectedValues }: any) => {
  filterForm.status = selectedValues[0]
  showStatusPicker.value = false
}

// 日期选择确认
const onDateConfirm = (values: any) => {
  if (values.length === 2) {
    filterForm.dateRange = `${formatDate(values[0])} - ${formatDate(values[1])}`
  }
  showDatePicker.value = false
}

// 查看记录详情
const viewRecordDetail = (record: any) => {
  selectedRecord.value = record
  showRecordDetail.value = true
}

// 预览照片
const previewPhoto = (url: string) => {
  previewPhotoUrl.value = url
  showPhotoPreview.value = true
}

// 格式化日期
const formatDate = (date: string | Date) => {
  const d = new Date(date)
  return `${d.getMonth() + 1}月${d.getDate()}日`
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}
</script>

<style scoped>
.admin-records-container {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100%;
  width: 100%;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-actions {
  padding: 16px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 16px 0;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 4px;
}

.stat-number.success {
  color: #07c160;
}

.stat-number.failed {
  color: #ee0a24;
}

.stat-label {
  font-size: 12px;
  color: #969799;
}

.records-section {
  margin-bottom: 20px;
}

.record-detail-popup {
  height: 100%;
  background: #f7f8fa;
}

.record-detail-content {
  padding: 16px;
  height: calc(100% - 46px);
  overflow-y: auto;
}

.photo-preview {
  padding: 16px;
  text-align: center;
}

:deep(.van-grid-item__content) {
  padding: 0;
}

:deep(.van-cell__value) {
  display: flex;
  align-items: center;
}
</style>
