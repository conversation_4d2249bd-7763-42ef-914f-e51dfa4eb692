import { createRouter, createWebHistory } from 'vue-router'
import type { RouteMeta } from '@/types'

// 扩展路由元信息类型
declare module 'vue-router' {
  interface RouteMeta extends RouteMeta {}
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/checkin'
    },
    {
      path: '/checkin',
      name: 'checkin',
      component: () => import('../views/CheckInView.vue'),
      meta: {
        title: '人脸签到',
        requiresAuth: false
      }
    },
    {
      path: '/home',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
      meta: {
        title: '管理首页',
        requiresAuth: true
      }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        title: '登录',
        requiresAuth: false,
        hideNavBar: true
      }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
      meta: {
        title: '注册',
        requiresAuth: false,
        hideNavBar: true
      }
    },
    {
      path: '/admin/photos',
      name: 'admin-photos',
      component: () => import('../views/AdminPhotosView.vue'),
      meta: {
        title: '照片管理',
        requiresAuth: true
      }
    },
    {
      path: '/admin/records',
      name: 'admin-records',
      component: () => import('../views/AdminRecordsView.vue'),
      meta: {
        title: '签到记录',
        requiresAuth: true
      }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: {
        title: '个人中心',
        requiresAuth: true
      }
    }
  ]
})

// 路由守卫 - 暂时关闭权限控制
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 人脸识别签到系统`
  }

  // 暂时允许所有页面访问，不管是否登录
  next()

  // 👇 原权限控制代码（已注释）
  // const token = localStorage.getItem('token')
  // const requiresAuth = to.meta.requiresAuth
  //
  // // 检查认证状态
  // if (requiresAuth && !token) {
  //   // 需要认证但未登录，跳转到登录页
  //   next('/login')
  // } else if (!requiresAuth && token && (to.name === 'login' || to.name === 'register')) {
  //   // 已登录用户访问登录/注册页，跳转到管理首页
  //   next('/home')
  // } else {
  //   next()
  // }
})

export default router
