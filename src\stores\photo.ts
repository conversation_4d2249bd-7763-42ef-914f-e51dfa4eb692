import { ref } from 'vue'
import { defineStore } from 'pinia'
import { photoApi } from '@/services/api'
import type { PhotoVO } from '@/types'

export const usePhotoStore = defineStore('photo', () => {
  // 状态
  const uploading = ref(false)
  const uploadProgress = ref(0)

  // 上传照片
  const uploadPhoto = async (file: File) => {
    uploading.value = true
    uploadProgress.value = 0

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        if (uploadProgress.value < 90) {
          uploadProgress.value += 10
        }
      }, 100)

      const response = await photoApi.uploadPhoto(file)
      
      clearInterval(progressInterval)
      uploadProgress.value = 100
      
      setTimeout(() => {
        uploading.value = false
        uploadProgress.value = 0
      }, 500)

      return {
        success: true,
        message: '照片上传成功',
        data: response.data.data as PhotoVO
      }
    } catch (error: any) {
      uploading.value = false
      uploadProgress.value = 0
      
      return {
        success: false,
        message: error.message || '照片上传失败'
      }
    }
  }

  // 压缩图片
  const compressImage = (file: File, quality: number = 0.8): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      const img = new Image()

      img.onload = () => {
        // 设置画布尺寸，限制最大宽度为800px
        const maxWidth = 800
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height)
        
        canvas.width = img.width * ratio
        canvas.height = img.height * ratio

        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)

        // 转换为Blob
        canvas.toBlob((blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: 'image/jpeg',
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            resolve(file)
          }
        }, 'image/jpeg', quality)
      }

      img.src = URL.createObjectURL(file)
    })
  }

  // 验证图片文件
  const validateImageFile = (file: File) => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png']
    const maxSize = 5 * 1024 * 1024 // 5MB

    if (!validTypes.includes(file.type)) {
      return {
        valid: false,
        message: '请选择 JPG、JPEG 或 PNG 格式的图片'
      }
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        message: '图片大小不能超过 5MB'
      }
    }

    return { valid: true, message: '' }
  }

  return {
    // 状态
    uploading,
    uploadProgress,

    // 方法
    uploadPhoto,
    compressImage,
    validateImageFile
  }
})
