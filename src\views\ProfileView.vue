<template>
  <div class="profile-container">
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="user-avatar">
        <van-image
          :src="userStore.user?.avatar || defaultAvatar"
          round
          width="80"
          height="80"
          fit="cover"
        />
        <van-button
          type="primary"
          size="mini"
          round
          class="edit-avatar-btn"
          @click="showToast('功能开发中...')"
        >
          编辑
        </van-button>
      </div>
      <div class="user-info">
        <h2>{{ userStore.user?.name || userStore.user?.username }}</h2>
        <p>{{ userStore.user?.email || '未设置邮箱' }}</p>
        <van-tag type="success" size="medium">活跃用户</van-tag>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <van-cell-group inset>
        <van-cell title="签到统计" />
        <van-grid :column-num="3" :border="false">
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ totalCheckIns }}</div>
              <div class="stat-label">总签到</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ monthlyCheckIns }}</div>
              <div class="stat-label">本月签到</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ successRate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </van-grid-item>
        </van-grid>
      </van-cell-group>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <van-cell-group inset>
        <van-cell
          title="个人信息"
          is-link
          @click="editProfile"
        >
          <template #icon>
            <van-icon name="user-o" />
          </template>
        </van-cell>
        <van-cell
          title="签到记录"
          is-link
          @click="viewCheckInHistory"
        >
          <template #icon>
            <van-icon name="clock-o" />
          </template>
        </van-cell>
        <van-cell
          title="人脸照片管理"
          is-link
          @click="manageFacePhotos"
        >
          <template #icon>
            <van-icon name="photograph" />
          </template>
        </van-cell>
        <van-cell
          title="系统设置"
          is-link
          @click="showToast('功能开发中...')"
        >
          <template #icon>
            <van-icon name="setting-o" />
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 其他功能 -->
    <div class="other-section">
      <van-cell-group inset>
        <van-cell
          title="帮助与反馈"
          is-link
          @click="showToast('功能开发中...')"
        >
          <template #icon>
            <van-icon name="question-o" />
          </template>
        </van-cell>
        <van-cell
          title="关于我们"
          is-link
          @click="showAbout"
        >
          <template #icon>
            <van-icon name="info-o" />
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 退出登录 -->
    <div class="logout-section">
      <van-button
        type="danger"
        size="large"
        round
        block
        @click="handleLogout"
        :loading="logoutLoading"
      >
        退出登录
      </van-button>
    </div>

    <!-- 个人信息编辑弹窗 -->
    <van-popup
      v-model:show="showEditProfile"
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="edit-profile-popup">
        <van-nav-bar
          title="编辑个人信息"
          left-text="取消"
          right-text="保存"
          @click-left="showEditProfile = false"
          @click-right="saveProfile"
        />
        <van-form class="edit-form">
          <van-cell-group inset>
            <van-field
              v-model="editForm.name"
              label="姓名"
              placeholder="请输入姓名"
            />
            <van-field
              v-model="editForm.email"
              label="邮箱"
              placeholder="请输入邮箱"
            />
            <van-field
              v-model="editForm.phone"
              label="手机号"
              placeholder="请输入手机号"
            />
          </van-cell-group>
        </van-form>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showDialog, showConfirmDialog } from 'vant'
import { useUserStore } from '@/stores/counter'

const router = useRouter()
const userStore = useUserStore()

// 默认头像
const defaultAvatar = 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'

// 统计数据
const totalCheckIns = ref(156)
const monthlyCheckIns = ref(23)
const successRate = ref(96)

// 弹窗状态
const showEditProfile = ref(false)
const logoutLoading = ref(false)

// 编辑表单
const editForm = reactive({
  name: '',
  email: '',
  phone: ''
})

// 编辑个人信息
const editProfile = () => {
  // 初始化表单数据
  editForm.name = userStore.user?.name || ''
  editForm.email = userStore.user?.email || ''
  editForm.phone = userStore.user?.phone || ''

  showEditProfile.value = true
}

// 保存个人信息
const saveProfile = async () => {
  try {
    // 这里应该调用API更新用户信息
    // const result = await userApi.updateProfile(editForm)

    // 模拟保存成功
    showToast.success('保存成功')
    showEditProfile.value = false

    // 更新本地用户信息
    if (userStore.user) {
      userStore.user.name = editForm.name
      userStore.user.email = editForm.email
      userStore.user.phone = editForm.phone
      localStorage.setItem('user', JSON.stringify(userStore.user))
    }
  } catch (error) {
    showToast.fail('保存失败，请重试')
  }
}

// 查看签到记录
const viewCheckInHistory = () => {
  showToast('功能开发中...')
}

// 管理人脸照片
const manageFacePhotos = () => {
  showToast('功能开发中...')
}

// 显示关于信息
const showAbout = () => {
  showDialog({
    title: '关于我们',
    message: `
人脸识别签到系统 v1.0.0

这是一个基于Vue 3和人脸识别技术的移动端签到应用。

开发团队：南京大学软件学院
技术栈：Vue 3 + TypeScript + Vant UI

感谢您的使用！
    `,
    confirmButtonText: '确定'
  })
}

// 处理退出登录
const handleLogout = async () => {
  try {
    const confirmed = await showConfirmDialog({
      title: '确认退出',
      message: '确定要退出登录吗？'
    })

    if (confirmed) {
      logoutLoading.value = true

      const result = await userStore.logout()

      if (result.success) {
        showToast.success('已退出登录')
        router.replace('/login')
      } else {
        showToast.fail(result.message)
      }
    }
  } catch (error) {
    // 用户取消操作
  } finally {
    logoutLoading.value = false
  }
}

// 加载用户数据
const loadUserData = async () => {
  // 这里可以调用API获取最新的用户统计数据
  // const result = await userApi.getUserStats()
  // if (result.success) {
  //   totalCheckIns.value = result.data.totalCheckIns
  //   monthlyCheckIns.value = result.data.monthlyCheckIns
  //   successRate.value = result.data.successRate
  // }
}

onMounted(() => {
  loadUserData()
})
</script>

<style scoped>
.profile-container {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100%;
  width: 100%;
}

.user-card {
  background: linear-gradient(135deg, #1989fa, #1976d2);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-avatar {
  position: relative;
}

.edit-avatar-btn {
  position: absolute;
  bottom: -5px;
  right: -5px;
  min-width: 40px;
  height: 24px;
  font-size: 12px;
}

.user-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: bold;
}

.user-info p {
  margin: 0 0 12px 0;
  opacity: 0.8;
  font-size: 14px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 16px 0;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969799;
}

.menu-section {
  margin-bottom: 20px;
}

.other-section {
  margin-bottom: 20px;
}

.logout-section {
  margin-bottom: 20px;
}

.edit-profile-popup {
  height: 100%;
  background: #f7f8fa;
}

.edit-form {
  padding: 16px;
}

:deep(.van-cell__left-icon) {
  color: #1989fa;
  margin-right: 12px;
}

:deep(.van-grid-item__content) {
  padding: 0;
}
</style>
