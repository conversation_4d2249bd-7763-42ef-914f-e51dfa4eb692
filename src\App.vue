<script setup lang="ts">
import { RouterView, useRoute, useRouter } from 'vue-router'
import { computed } from 'vue'
import { useUserStore } from './stores/counter'
import { showToast } from 'vant'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 是否显示导航栏
const showNavBar = computed(() => !route.meta.hideNavBar)

// 导航栏标题
const navTitle = computed(() => route.meta.title || '人脸识别签到系统')

// 全局导航菜单项
const allNavItems = [
  { name: 'checkin', label: '人脸签到', icon: 'photograph', requiresAuth: false },
  { name: 'home', label: '管理首页', icon: 'home-o', requiresAuth: true },
  { name: 'admin-photos', label: '照片管理', icon: 'photo-o', requiresAuth: true },
  { name: 'admin-records', label: '签到记录', icon: 'clock-o', requiresAuth: true },
  { name: 'profile', label: '个人中心', icon: 'user-o', requiresAuth: true }
]

// 底部导航菜单（根据登录状态过滤）
const tabbarItems = computed(() => {
  if (!userStore.isLoggedIn) {
    // 未登录时只显示签到页面
    return allNavItems.filter(item => !item.requiresAuth)
  }
  // 已登录时显示所有页面
  return allNavItems
})

// 处理导航栏返回
const onClickLeft = () => {
  history.back()
}

// 处理导航栏右侧按钮
const onClickRight = () => {
  if (route.name === 'profile') {
    // 在个人中心页面显示设置菜单
    showToast('设置功能开发中...')
  } else if (!userStore.isLoggedIn) {
    // 未登录时显示登录按钮
    router.push('/login')
  }
}

// 导航栏右侧按钮文本
const rightText = computed(() => {
  if (route.name === 'profile') {
    return '设置'
  } else if (!userStore.isLoggedIn && route.name !== 'login' && route.name !== 'register') {
    return '登录'
  }
  return ''
})
</script>

<template>
  <div class="app">
    <!-- 导航栏 -->
    <van-nav-bar
      v-if="showNavBar"
      :title="navTitle"
      left-arrow
      :right-text="rightText"
      @click-left="onClickLeft"
      @click-right="onClickRight"
      fixed
      placeholder
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <RouterView />
    </div>

    <!-- 底部导航 -->
    <van-tabbar
      v-if="showNavBar"
      route
      fixed
      placeholder
    >
      <van-tabbar-item
        v-for="item in tabbarItems"
        :key="item.name"
        :to="{ name: item.name }"
        :icon="item.icon"
      >
        {{ item.label }}
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.main-content {
  min-height: calc(100vh - 46px - 50px); /* 减去导航栏和底部导航的高度 */
  padding-bottom: env(safe-area-inset-bottom);
}

/* 当没有底部导航时 */
.main-content:last-child {
  min-height: calc(100vh - 46px);
}
</style>
