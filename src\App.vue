<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { computed } from 'vue'
import { useUserStore } from './stores/counter'
import { showToast } from 'vant'

const route = useRoute()
const userStore = useUserStore()

// 是否显示导航栏
const showNavBar = computed(() => !route.meta.hideNavBar)

// 导航栏标题
const navTitle = computed(() => route.meta.title || '人脸识别签到系统')

// 底部导航菜单
const tabbarItems = [
  { name: 'home', label: '首页', icon: 'home-o' },
  { name: 'checkin', label: '签到', icon: 'photograph' },
  { name: 'profile', label: '我的', icon: 'user-o' }
]

// 处理导航栏返回
const onClickLeft = () => {
  history.back()
}

// 处理导航栏右侧按钮
const onClickRight = () => {
  if (route.name === 'profile') {
    // 在个人中心页面显示设置菜单
    showToast('设置功能开发中...')
  }
}
</script>

<template>
  <div class="app">
    <!-- 导航栏 -->
    <van-nav-bar
      v-if="showNavBar"
      :title="navTitle"
      left-arrow
      @click-left="onClickLeft"
      @click-right="onClickRight"
      fixed
      placeholder
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <RouterView />
    </div>

    <!-- 底部导航 -->
    <van-tabbar
      v-if="showNavBar && userStore.isLoggedIn"
      route
      fixed
      placeholder
    >
      <van-tabbar-item
        v-for="item in tabbarItems"
        :key="item.name"
        :to="{ name: item.name }"
        :icon="item.icon"
      >
        {{ item.label }}
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.main-content {
  min-height: calc(100vh - 46px - 50px); /* 减去导航栏和底部导航的高度 */
  padding-bottom: env(safe-area-inset-bottom);
}

/* 当没有底部导航时 */
.main-content:last-child {
  min-height: calc(100vh - 46px);
}
</style>
