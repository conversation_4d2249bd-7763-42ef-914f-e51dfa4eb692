<template>
  <div class="login-container">
    <div class="login-header">
      <div class="logo">
        <van-icon name="user-circle-o" size="80" color="#1989fa" />
      </div>
      <h1 class="title">人脸识别签到系统</h1>
      <p class="subtitle">请登录您的账户</p>
    </div>

    <van-form @submit="handleLogin" class="login-form">
      <van-cell-group inset>
        <van-field
          v-model="loginForm.username"
          name="username"
          label="用户名"
          placeholder="请输入用户名"
          :rules="[{ required: true, message: '请输入用户名' }]"
          left-icon="user-o"
          clearable
        />
        <van-field
          v-model="loginForm.password"
          type="password"
          name="password"
          label="密码"
          placeholder="请输入密码"
          :rules="[{ required: true, message: '请输入密码' }]"
          left-icon="lock"
          clearable
        />
      </van-cell-group>

      <div class="login-actions">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="loading"
          loading-text="登录中..."
          size="large"
        >
          登录
        </van-button>
      </div>
    </van-form>

    <div class="login-footer">
      <van-divider>还没有账户？</van-divider>
      <van-button
        type="default"
        size="large"
        round
        block
        @click="goToRegister"
      >
        立即注册
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/counter'
import type { LoginRequest } from '@/types'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const loginForm = reactive<LoginRequest>({
  username: '',
  password: ''
})

// 加载状态
const loading = ref(false)

// 处理登录
const handleLogin = async () => {
  loading.value = true
  
  try {
    const result = await userStore.login(loginForm)
    
    if (result.success) {
      showToast.success(result.message)
      // 登录成功后跳转到首页
      router.replace('/home')
    } else {
      showToast.fail(result.message)
    }
  } catch (error) {
    showToast.fail('登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 跳转到注册页面
const goToRegister = () => {
  router.push('/register')
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  margin-bottom: 20px;
}

.title {
  color: white;
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 10px 0;
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

.login-form {
  margin-bottom: 30px;
}

.login-actions {
  padding: 20px 16px 0;
}

.login-footer {
  padding: 0 16px;
}

:deep(.van-cell-group) {
  margin-bottom: 0;
}

:deep(.van-field__left-icon) {
  color: #1989fa;
}

:deep(.van-divider) {
  color: rgba(255, 255, 255, 0.8);
  border-color: rgba(255, 255, 255, 0.3);
}
</style>
